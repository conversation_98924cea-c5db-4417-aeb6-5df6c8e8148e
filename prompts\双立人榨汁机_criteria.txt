```
---

这是用户的【购买需求】：
```text
全新未拆封  包装完好
```
---

请现在开始生成全新的【分析标准】文本。请注意：
1.  **只输出新生成的文本内容**，不要包含任何额外的解释、标题或代码块标记。
2.  保留范例中的 `[V6.3 核心升级]`、`[V6.4 逻辑修正]` 等版本标记，这有助于保持格式一致性。
3.  将范例中所有与 "MacBook" 相关的内容，替换为与用户需求商品相关的内容。
4.  思考并生成针对新商品类型的“一票否决硬性原则”和“危险信号清单”。

```
---

这是根据用户需求生成的新的【分析标准】文本：

### **第一部分：商品本身评估**

1.  **型号芯片 (`model_chip`)**: 核对所有文本和图片。必须是最新款且符合用户需求的型号。非最新款或不符合用户需求的型号视为 `FAIL`。
2.  **电池健康 (`battery_health`)**: 健康度 ≥ 90%。若无信息，则为 `NEEDS_MANUAL_CHECK`。
3.  **成色外观 (`condition`)**: 检查所有可见部分，包括屏幕、键盘、外壳等。不允许有任何明显的划痕、凹陷、损坏或磨损。
4.  **【V6.4 逻辑修正】机器历史 (`history`)**: 严格审查所有文本和图片，寻找“换过”、“维修”、“拆过”、“进水”、“功能不正常”等负面描述。**若完全未提及，则状态为 `NEEDS_MANUAL_CHECK`**；若有任何拆修证据，则为 `FAIL`。

### **第二部分：卖家与市场评估 (核心)**

5.  **卖家背景深度分析 (`seller_type`) - [决定性评估]**:
    *   **核心目标**: 运用“画像优先原则”，判定卖家是【个人玩家】还是【商家/贩子】。
    *   **【V6.3 升级】危险信号清单 (Red Flag List) 及豁免条款**:
        *   **交易频率**: 短期内频繁交易。
            *   **【发烧友豁免条款】**: 如果交易记录时间跨度长（如超过2年），且买卖行为能形成“体验-升级-出售”的逻辑闭环，则此条不适用。一个长期发烧友在几年内有数十次交易是正常的。
        *   **商品垂直度**: 发布的商品高度集中于某一特定型号或品类。
            *   **【发烧友豁免条款】**: 如果卖家是该领域的深度玩家（例如，从他的购买记录、评价和发言能看出），专注于某个系列是他的专业性的体现。关键看他是“玩”还是在“出货”。
        *   **“行话”**: 描述中出现“同行、工作室、拿货、量大从优”等术语。
            *   **【无豁免】**: 此为强烈的商家信号。
        *   **物料购买**: 购买记录中出现批量配件、维修工具、坏机等。
            *   **【无豁免】**: 此为决定性的商家证据。
        *   **图片/标题风格**: 图片背景高度统一、专业；或标题模板化。
            *   **【发烧友豁免条款】**: 如果卖家追求完美，有自己的“摄影棚”或固定角落来展示他心爱的物品，这是加分项。关键看图片传递的是“爱惜感”还是“商品感”。

6.  **邮寄方式 (`shipping`)**: 明确“仅限xx地面交/自提”则 `FAIL`。
7.  **卖家信用 (`seller_credit`)**: 卖家信用等级必须为 **'卖家信用极好'**。

```